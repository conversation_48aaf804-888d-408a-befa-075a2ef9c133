"""
数据库操作类
处理MySQL数据库连接和操作
"""
import pymysql
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from config import DATABASE_CONFIG

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.connection = None
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def connect(self, create_db=False) -> bool:
        """连接数据库"""
        try:
            if create_db:
                # 连接时不指定数据库，用于创建数据库
                config = DATABASE_CONFIG.copy()
                db_name = config.pop('database')
                self.connection = pymysql.connect(**config)
                self.logger.info("数据库服务器连接成功")
                return True
            else:
                self.connection = pymysql.connect(**DATABASE_CONFIG)
                self.logger.info("数据库连接成功")
                return True
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            self.logger.info("数据库连接已断开")
    
    def execute_query(self, query: str, params: tuple = None) -> Optional[List[Dict]]:
        """执行查询语句"""
        if not self.connection:
            if not self.connect():
                return None
        
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(query, params)
                result = cursor.fetchall()
                return result
        except Exception as e:
            self.logger.error(f"查询执行失败: {e}")
            return None
    
    def execute_update(self, query: str, params: tuple = None) -> bool:
        """执行更新语句"""
        if not self.connection:
            if not self.connect():
                return False
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
                self.connection.commit()
                return True
        except Exception as e:
            self.logger.error(f"更新执行失败: {e}")
            self.connection.rollback()
            return False
    
    def create_tables(self) -> bool:
        """创建数据库表"""
        tables = {
            'users': """
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    login_attempts INT DEFAULT 0,
                    locked_until TIMESTAMP NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """,
            'emotion_records': """
                CREATE TABLE IF NOT EXISTS emotion_records (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    emotion VARCHAR(20) NOT NULL,
                    confidence FLOAT NOT NULL,
                    probabilities JSON NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    session_id VARCHAR(100),
                    screenshot_path VARCHAR(255),
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    INDEX idx_user_timestamp (user_id, timestamp),
                    INDEX idx_emotion (emotion),
                    INDEX idx_session (session_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """,
            'user_sessions': """
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    session_id VARCHAR(100) UNIQUE NOT NULL,
                    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    end_time TIMESTAMP NULL,
                    duration INT DEFAULT 0,
                    total_detections INT DEFAULT 0,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    INDEX idx_user_session (user_id, session_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """,
            'emotion_statistics': """
                CREATE TABLE IF NOT EXISTS emotion_statistics (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    date DATE NOT NULL,
                    emotion VARCHAR(20) NOT NULL,
                    count INT DEFAULT 0,
                    avg_confidence FLOAT DEFAULT 0,
                    total_duration INT DEFAULT 0,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_user_date_emotion (user_id, date, emotion),
                    INDEX idx_user_date (user_id, date)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """
        }
        
        for table_name, create_sql in tables.items():
            if not self.execute_update(create_sql):
                self.logger.error(f"创建表 {table_name} 失败")
                return False
            self.logger.info(f"表 {table_name} 创建成功")
        
        return True
    
    def init_database(self) -> bool:
        """初始化数据库"""
        # 首先连接到MySQL服务器（不指定数据库）
        if not self.connect(create_db=True):
            return False

        # 创建数据库（如果不存在）
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DATABASE_CONFIG['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                cursor.execute(f"USE {DATABASE_CONFIG['database']}")
                self.connection.commit()
                self.logger.info(f"数据库 {DATABASE_CONFIG['database']} 创建/选择成功")
        except Exception as e:
            self.logger.error(f"创建数据库失败: {e}")
            return False

        # 创建表
        return self.create_tables()
