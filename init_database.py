"""
数据库初始化脚本
用于创建数据库和表结构
"""
import os
import sys

def check_mysql_service():
    """检查MySQL服务是否运行"""
    import subprocess
    try:
        # Windows
        if os.name == 'nt':
            result = subprocess.run(['sc', 'query', 'mysql'],
                                  capture_output=True, text=True)
            if 'RUNNING' in result.stdout:
                return True
            # 尝试其他常见的MySQL服务名
            for service_name in ['MySQL80', 'MySQL57', 'MySQL56', 'MariaDB']:
                result = subprocess.run(['sc', 'query', service_name],
                                      capture_output=True, text=True)
                if 'RUNNING' in result.stdout:
                    return True
        else:
            # Linux/Mac
            result = subprocess.run(['systemctl', 'is-active', 'mysql'],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                return True
    except:
        pass
    return False


def create_env_file():
    """创建.env文件"""
    if os.path.exists('.env'):
        print(".env文件已存在")
        return

    print("创建.env配置文件...")

    # 获取用户输入
    db_host = "localhost"
    db_port = "3306"
    db_user = "root"
    db_password = "Sshen2003821"
    db_name = "emotion_recognition"

    # 创建.env文件
    env_content = f"""# 数据库配置
DB_HOST={db_host}
DB_PORT={db_port}
DB_USER={db_user}
DB_PASSWORD={db_password}
DB_NAME={db_name}

# 应用程序配置
SECRET_KEY=your-secret-key-here-{os.urandom(16).hex()}
"""

    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)

    print("✓ .env文件创建成功")


def test_database_connection():
    """测试数据库连接"""
    print("测试数据库服务器连接...")

    try:
        from database import DatabaseManager
        db_manager = DatabaseManager()
        # 先测试服务器连接（不指定数据库）
        if db_manager.connect(create_db=True):
            print("✓ 数据库服务器连接成功")
            db_manager.disconnect()
            return True
        else:
            print("✗ 数据库服务器连接失败")
            return False
    except Exception as e:
        print(f"✗ 数据库连接测试失败: {e}")
        return False


def initialize_database():
    """初始化数据库"""
    print("初始化数据库...")

    try:
        from database import DatabaseManager
        db_manager = DatabaseManager()
        if db_manager.init_database():
            print("✓ 数据库初始化成功")
            db_manager.disconnect()
            return True
        else:
            print("✗ 数据库初始化失败")
            db_manager.disconnect()
            return False
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("情绪识别系统 - 数据库初始化")
    print("=" * 50)

    # 检查MySQL服务
    print("检查MySQL服务状态...")
    if not check_mysql_service():
        print("⚠ 警告: 未检测到MySQL服务运行")
        print("请确保MySQL服务已启动:")
        print("  Windows: 在服务管理器中启动MySQL服务")
        print("  Linux: sudo systemctl start mysql")
        print("  macOS: brew services start mysql")
        print()

        choice = input("是否继续初始化? (y/n): ").lower()
        if choice != 'y':
            return False
    else:
        print("✓ MySQL服务正在运行")

    print()

    # 检查依赖
    try:
        import pymysql
        print("✓ PyMySQL 已安装")
    except ImportError:
        print("✗ PyMySQL 未安装，请运行: pip install PyMySQL")
        return False

    try:
        import bcrypt
        print("✓ bcrypt 已安装")
    except ImportError:
        print("✗ bcrypt 未安装，请运行: pip install bcrypt")
        return False

    try:
        from dotenv import load_dotenv
        print("✓ python-dotenv 已安装")
    except ImportError:
        print("✗ python-dotenv 未安装，请运行: pip install python-dotenv")
        return False

    print()

    # 创建配置文件
    create_env_file()
    print()

    # 重新加载环境变量
    from dotenv import load_dotenv
    load_dotenv()

    # 导入数据库模块
    try:
        from database import DatabaseManager
        from config import DATABASE_CONFIG
    except ImportError as e:
        print(f"✗ 导入模块失败: {e}")
        return False

    # 测试连接
    if not test_database_connection():
        print("\n请检查数据库配置和连接信息")
        print("常见问题:")
        print("1. 确认MySQL服务已启动")
        print("2. 检查用户名和密码是否正确")
        print("3. 确认数据库服务器地址和端口")
        return False

    print()

    # 初始化数据库
    if not initialize_database():
        print("\n数据库初始化失败")
        return False

    print()
    print("=" * 50)
    print("数据库初始化完成！")
    print("现在可以运行情绪识别系统了")
    print("=" * 50)

    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n操作已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n初始化过程中发生错误: {e}")
        sys.exit(1)
