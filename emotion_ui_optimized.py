import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import cv2
import mindspore as ms
from mindspore import Tensor
from mindspore.train.serialization import load_checkpoint, load_param_into_net
import numpy as np
from mindspore.common.initializer import Normal
import mindspore.nn as nn
import random
from PIL import Image, ImageTk
import threading
import datetime
import os
from pathlib import Path
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.pyplot as plt
import matplotlib
import matplotlib.font_manager as fm


# 配置中文字体
def setup_chinese_font():
    """设置中文字体"""
    try:
        # 尝试使用本地的SimHei字体文件
        font_path = "D:\2025-up\computer see\EmotionUi\SimHei.ttf"
        if os.path.exists(font_path):
            prop = fm.FontProperties(fname=font_path)
            matplotlib.rcParams['font.family'] = prop.get_name()
            print(f"使用本地字体文件: {font_path}")
        else:
            # 使用系统字体
            matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
            print("使用系统字体")
    except Exception as e:
        print(f"字体设置失败: {e}")
        # 备用字体设置
        matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans']

    matplotlib.rcParams['axes.unicode_minus'] = False


# 设置字体
setup_chinese_font()

# 表情类别
categories = ['生气', '开心', '悲伤']

# 情绪颜色映射
emotion_colors = {
    '生气': '#FF5252',  # 红色
    '开心': '#4CAF50',  # 绿色
    '悲伤': '#2196F3',  # 蓝色
}

# 情绪图标映射
emotion_icons = {
    '生气': '😠',
    '开心': '😄',
    '悲伤': '😢'
}

# 情绪建议映射
emotion_suggestions = {
    '生气': [
        "深呼吸，慢慢数到十",
        "听一些舒缓的音乐",
        "出去散步，呼吸新鲜空气",
        "做一些放松的运动，如瑜伽",
        "与朋友聊天，分享你的感受"
    ],
    '开心': [
        "保持这种积极的心态！",
        "分享你的快乐给身边的人",
        "做一些你喜欢的事情",
        "记录下这美好的时刻",
        "继续保持乐观的态度"
    ],
    '悲伤': [
        "允许自己感受这种情绪",
        "找一个安静的地方休息一下",
        "与信任的朋友或家人交谈",
        "做一些让你感到舒适的事情",
        "记住这种感觉是暂时的"
    ]
}

# 人脸检测器
try:
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
except:
    face_cascade = cv2.CascadeClassifier('haarcascade_frontalface_default.xml')


# 定义模型
class CNN(nn.Cell):
    def __init__(self):
        super(CNN, self).__init__()
        self.conv1 = nn.Conv2d(3, 32, 3, pad_mode='same', weight_init=Normal(0.02))
        self.conv2 = nn.Conv2d(32, 64, 3, pad_mode='same', weight_init=Normal(0.02))
        self.conv3 = nn.Conv2d(64, 128, 3, pad_mode='same', weight_init=Normal(0.02))
        self.fc1 = nn.Dense(128 * 6 * 6, 512)
        self.fc2 = nn.Dense(512, len(categories))
        self.relu = nn.ReLU()
        self.max_pool2d = nn.MaxPool2d(kernel_size=2, stride=2)
        self.flatten = nn.Flatten()

    def construct(self, x):
        x = self.conv1(x)
        x = self.relu(x)
        x = self.max_pool2d(x)
        x = self.conv2(x)
        x = self.relu(x)
        x = self.max_pool2d(x)
        x = self.conv3(x)
        x = self.relu(x)
        x = self.max_pool2d(x)
        x = self.flatten(x)
        x = self.fc1(x)
        x = self.relu(x)
        x = self.fc2(x)
        return x


# 优化后的情绪识别UI类
class EmotionUI:
    def __init__(self, master):
        self.master = master
        self.master.title("情绪识别系统")
        self.master.geometry("1024x768")

        # 初始化变量
        self.is_running = False
        self.cap = None
        self.current_emotion = "未知"
        self.probabilities = [0.0] * len(categories)
        self.emotion_history = []
        self.emotion_timestamps = []

        # 创建数据目录
        self.data_dir = Path("emotion_data")
        self.data_dir.mkdir(exist_ok=True)
        self.screenshots_dir = self.data_dir / "screenshots"
        self.screenshots_dir.mkdir(exist_ok=True)

        # 界面框架
        self.frames = {}

        # 设置
        self.settings = {
            "alert_enabled": True,
            "alert_thresholds": {"生气": 0.85, "悲伤": 0.80},
            "camera_index": 0,
            "auto_save_screenshots": False,
            "screenshot_interval": 60
        }

        # 加载模型
        self.load_model()

        # 设置主题
        self.setup_theme()

        # 创建界面
        self.create_main_menu()
        self.create_detection_ui()
        self.create_settings_ui()
        self.create_history_ui()

        # 显示主菜单
        self.show_main_menu()

    def load_model(self):
        """加载情绪识别模型"""
        global net
        net = CNN()

        try:
            param_dict = load_checkpoint("emotion_model.ckpt")
            load_param_into_net(net, param_dict)
            print("已加载预训练模型")
        except Exception as e:
            print(f"加载预训练模型失败: {e}")
            messagebox.showwarning("警告", "无法加载预训练模型，将使用未训练的模型")

    def setup_theme(self):
        """设置应用主题和样式"""
        style = ttk.Style()

        try:
            style.theme_use('clam')
        except:
            pass

        # 获取中文字体
        chinese_font = self.get_chinese_font()

        # 自定义样式 - 使用中文字体
        style.configure('Title.TLabel', font=(chinese_font, 18, 'bold'))
        style.configure('Subtitle.TLabel', font=(chinese_font, 12))
        style.configure('Large.TButton', font=(chinese_font, 12))
        style.configure('Status.TLabel', font=(chinese_font, 10))

        # 设置颜色
        style.configure('Title.TLabel', foreground='#2E3440')
        style.configure('Large.TButton', padding=(10, 5))

    def get_chinese_font(self):
        """获取可用的中文字体"""
        # 尝试不同的中文字体（使用正确的字体名称）
        fonts_to_try = [
            '微软雅黑',  # Microsoft YaHei
            '宋体',  # SimSun
            '黑体',  # SimHei
            '楷体',  # KaiTi
            '仿宋',  # FangSong
            'Microsoft YaHei UI',
            'Segoe UI',
            'Arial Unicode MS'
        ]

        # 检查系统字体
        import tkinter.font as tkFont
        available_fonts = tkFont.families()

        for font in fonts_to_try:
            if font in available_fonts:
                print(f"使用字体: {font}")
                return font

        # 如果都没有，返回默认字体
        print("使用默认字体: TkDefaultFont")
        return "TkDefaultFont"

    def create_main_menu(self):
        """创建主菜单界面"""
        self.frames['main_menu'] = ttk.Frame(self.master)
        main_menu = self.frames['main_menu']

        # 标题
        title_frame = ttk.Frame(main_menu)
        title_frame.pack(fill=tk.X, pady=20)

        ttk.Label(title_frame, text="情绪识别系统", style='Title.TLabel').pack()
        ttk.Label(title_frame, text="基于深度学习的实时情绪检测", style='Subtitle.TLabel').pack(pady=5)

        # 功能按钮
        button_frame = ttk.Frame(main_menu)
        button_frame.pack(pady=30)

        btn_width = 20
        btn_padding = 15

        # 开始检测按钮
        start_btn = ttk.Button(button_frame, text="开始情绪检测",
                               command=self.start_detection,
                               width=btn_width, style='Large.TButton')
        start_btn.grid(row=0, column=0, padx=btn_padding, pady=btn_padding)

        # 历史记录按钮
        history_btn = ttk.Button(button_frame, text="查看情绪历史",
                                 command=self.show_history,
                                 width=btn_width, style='Large.TButton')
        history_btn.grid(row=0, column=1, padx=btn_padding, pady=btn_padding)

        # 设置按钮
        settings_btn = ttk.Button(button_frame, text="系统设置",
                                  command=self.show_settings,
                                  width=btn_width, style='Large.TButton')
        settings_btn.grid(row=1, column=0, padx=btn_padding, pady=btn_padding)

        # 文件夹识别按钮
        folder_btn = ttk.Button(button_frame, text="文件夹识别",
                                command=self.open_folder_and_recognize,
                                width=btn_width, style='Large.TButton')
        folder_btn.grid(row=1, column=1, padx=btn_padding, pady=btn_padding)

        # 退出按钮
        exit_btn = ttk.Button(button_frame, text="退出系统",
                              command=self.safe_exit,
                              width=btn_width, style='Large.TButton')
        exit_btn.grid(row=2, column=0, columnspan=2, pady=btn_padding)

        # 功能描述
        desc_frame = ttk.Frame(main_menu)
        desc_frame.pack(fill=tk.X, pady=20)

        features = [
            "• 实时情绪检测和分析",
            "• 情绪历史记录和趋势分析",
            "• 智能警报和建议系统",
            "• 自动截图保存功能",
            "• 批量图片情绪识别"
        ]

        for feature in features:
            ttk.Label(desc_frame, text=feature, font=('微软雅黑', 12)).pack(anchor=tk.W, pady=3)

    def show_main_menu(self):
        """显示主菜单"""
        self.hide_all_frames()
        self.frames['main_menu'].pack(fill=tk.BOTH, expand=True)

    def hide_all_frames(self):
        """隐藏所有框架"""
        for frame in self.frames.values():
            frame.pack_forget()

    def create_detection_ui(self):
        """创建检测界面"""
        self.frames['detection'] = ttk.Frame(self.master)
        detection = self.frames['detection']

        # 顶部控制栏
        control_frame = ttk.Frame(detection)
        control_frame.pack(fill=tk.X, pady=5)

        ttk.Button(control_frame, text="返回主菜单",
                   command=self.show_main_menu).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="开始检测",
                   command=self.toggle_detection).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="保存截图",
                   command=self.save_screenshot).pack(side=tk.LEFT, padx=5)

        # 主要内容区域
        main_content = ttk.Frame(detection)
        main_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 左侧视频区域
        left_frame = ttk.Frame(main_content)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 获取中文字体
        chinese_font = self.get_chinese_font()

        # 视频显示
        self.video_label = ttk.Label(left_frame, text="摄像头未启动",
                                     background="black", foreground="white",
                                     font=(chinese_font, 12))
        self.video_label.pack(pady=10)

        # 右侧信息区域
        right_frame = ttk.Frame(main_content, width=300)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_frame.pack_propagate(False)

        # 当前情绪状态
        emotion_frame = ttk.LabelFrame(right_frame, text="当前情绪状态", padding="10")
        emotion_frame.pack(fill=tk.X, pady=5)

        self.emotion_var = tk.StringVar(value="未知")
        self.confidence_var = tk.StringVar(value="0.00")

        ttk.Label(emotion_frame, text="检测到的情绪:", font=(chinese_font, 12)).pack(anchor=tk.W)
        self.emotion_label = ttk.Label(emotion_frame, textvariable=self.emotion_var,
                                       font=(chinese_font, 16, 'bold'))
        self.emotion_label.pack(anchor=tk.W, pady=5)

        ttk.Label(emotion_frame, text="置信度:", font=(chinese_font, 12)).pack(anchor=tk.W)
        ttk.Label(emotion_frame, textvariable=self.confidence_var,
                  font=(chinese_font, 12)).pack(anchor=tk.W)

        # 情绪建议
        suggestion_frame = ttk.LabelFrame(right_frame, text="情绪建议", padding="10")
        suggestion_frame.pack(fill=tk.X, pady=5)

        self.suggestion_var = tk.StringVar(value="等待检测...")
        self.suggestion_label = ttk.Label(suggestion_frame, textvariable=self.suggestion_var,
                                          font=(chinese_font, 11), wraplength=250)
        self.suggestion_label.pack()

        # 概率显示
        prob_frame = ttk.LabelFrame(right_frame, text="情绪概率", padding="10")
        prob_frame.pack(fill=tk.X, pady=5)

        self.prob_vars = {}
        for i, emotion in enumerate(categories):
            self.prob_vars[emotion] = tk.StringVar(value="0.00")
            ttk.Label(prob_frame, text=f"{emotion}:",
                      font=(chinese_font, 10)).grid(row=i, column=0, sticky=tk.W)
            ttk.Label(prob_frame, textvariable=self.prob_vars[emotion],
                      font=(chinese_font, 10)).grid(row=i, column=1, sticky=tk.W, padx=10)

        # 警报显示区域
        alert_frame = ttk.LabelFrame(right_frame, text="情绪警报", padding="10")
        alert_frame.pack(fill=tk.X, pady=5)

        self.alert_var = tk.StringVar(value="无警报")
        self.alert_label = ttk.Label(alert_frame, textvariable=self.alert_var,
                                     font=(chinese_font, 10), wraplength=250,
                                     foreground="green")
        self.alert_label.pack()

        # 状态栏
        self.status = ttk.Label(detection, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status.pack(side=tk.BOTTOM, fill=tk.X)

    def create_settings_ui(self):
        """创建设置界面"""
        self.frames['settings'] = ttk.Frame(self.master)
        settings = self.frames['settings']

        # 顶部控制栏
        control_frame = ttk.Frame(settings)
        control_frame.pack(fill=tk.X, pady=5)

        ttk.Button(control_frame, text="返回主菜单",
                   command=self.show_main_menu).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="保存设置",
                   command=self.save_settings).pack(side=tk.LEFT, padx=5)

        # 设置内容
        main_frame = ttk.Frame(settings)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 警报设置
        alert_frame = ttk.LabelFrame(main_frame, text="警报设置", padding="10")
        alert_frame.pack(fill=tk.X, pady=10)

        self.alert_enabled_var = tk.BooleanVar(value=self.settings["alert_enabled"])
        ttk.Checkbutton(alert_frame, text="启用情绪警报",
                        variable=self.alert_enabled_var).pack(anchor=tk.W)

        # 阈值设置
        threshold_frame = ttk.LabelFrame(main_frame, text="警报阈值", padding="10")
        threshold_frame.pack(fill=tk.X, pady=10)

        self.anger_threshold_var = tk.DoubleVar(value=self.settings["alert_thresholds"]["生气"])
        self.sad_threshold_var = tk.DoubleVar(value=self.settings["alert_thresholds"]["悲伤"])

        ttk.Label(threshold_frame, text="生气阈值:").grid(row=0, column=0, sticky=tk.W)
        ttk.Scale(threshold_frame, from_=0.5, to=1.0, variable=self.anger_threshold_var,
                  orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=10)
        ttk.Label(threshold_frame, textvariable=self.anger_threshold_var).grid(row=0, column=2)

        ttk.Label(threshold_frame, text="悲伤阈值:").grid(row=1, column=0, sticky=tk.W)
        ttk.Scale(threshold_frame, from_=0.5, to=1.0, variable=self.sad_threshold_var,
                  orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=10)
        ttk.Label(threshold_frame, textvariable=self.sad_threshold_var).grid(row=1, column=2)

        threshold_frame.columnconfigure(1, weight=1)

        # 摄像头设置
        camera_frame = ttk.LabelFrame(main_frame, text="摄像头设置", padding="10")
        camera_frame.pack(fill=tk.X, pady=10)

        self.camera_index_var = tk.IntVar(value=self.settings["camera_index"])
        ttk.Label(camera_frame, text="摄像头索引:").pack(side=tk.LEFT)
        ttk.Spinbox(camera_frame, from_=0, to=5, textvariable=self.camera_index_var,
                    width=5).pack(side=tk.LEFT, padx=10)
        ttk.Button(camera_frame, text="测试摄像头",
                   command=self.test_camera).pack(side=tk.LEFT, padx=10)

    def create_history_ui(self):
        """创建历史记录界面"""
        self.frames['history'] = ttk.Frame(self.master)
        history = self.frames['history']

        # 顶部控制栏
        control_frame = ttk.Frame(history)
        control_frame.pack(fill=tk.X, pady=5)

        ttk.Button(control_frame, text="返回主菜单",
                   command=self.show_main_menu).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="刷新数据",
                   command=self.load_history_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除历史",
                   command=self.clear_history).pack(side=tk.LEFT, padx=5)

        # 主要内容
        main_content = ttk.Frame(history)
        main_content.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 历史记录列表
        list_frame = ttk.LabelFrame(main_content, text="情绪历史记录", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 创建列表框和滚动条
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)

        chinese_font = self.get_chinese_font()
        self.history_listbox = tk.Listbox(list_container, font=(chinese_font, 10))
        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, command=self.history_listbox.yview)
        self.history_listbox.configure(yscrollcommand=scrollbar.set)

        self.history_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def start_detection(self):
        """开始检测"""
        self.show_detection()
        self.init_camera()

    def show_detection(self):
        """显示检测界面"""
        self.hide_all_frames()
        self.frames['detection'].pack(fill=tk.BOTH, expand=True)

    def show_settings(self):
        """显示设置界面"""
        self.hide_all_frames()
        self.frames['settings'].pack(fill=tk.BOTH, expand=True)

    def show_history(self):
        """显示历史界面"""
        self.hide_all_frames()
        self.frames['history'].pack(fill=tk.BOTH, expand=True)
        self.load_history_data()

    def init_camera(self):
        """初始化摄像头"""
        camera_index = self.settings.get("camera_index", 0)
        self.cap = cv2.VideoCapture(camera_index)

        if not self.cap.isOpened():
            # 尝试其他摄像头索引
            for i in range(5):
                if i != camera_index:
                    self.cap = cv2.VideoCapture(i)
                    if self.cap.isOpened():
                        self.settings["camera_index"] = i
                        messagebox.showinfo("摄像头检测", f"已自动切换到摄像头 {i}")
                        break

        if not self.cap.isOpened():
            messagebox.showerror("错误", "无法打开摄像头，请检查设备连接")
            self.show_main_menu()
        else:
            self.update_status("摄像头就绪 | 点击[开始检测]启动识别")
            self.start_video_loop()

    def start_video_loop(self):
        """开始视频循环"""
        self.is_running = True
        self.video_thread = threading.Thread(target=self.video_loop, daemon=True)
        self.video_thread.start()

    def video_loop(self):
        """视频处理循环"""
        while self.is_running and self.cap and self.cap.isOpened():
            ret, frame = self.cap.read()
            if not ret:
                break

            # 识别情绪
            result = self.recognize_emotion(frame)

            if result:
                emotion = result['emotion']
                confidence = result['confidence']
                probabilities = result['probabilities']
                x, y, w, h = result['face_location']

                # 在视频上绘制人脸框和情绪标签
                cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)

                # 使用PIL绘制中文文字（解决OpenCV中文乱码问题）
                frame = self.draw_chinese_text(frame, emotion, confidence, (x, y - 10))

                # 更新UI显示
                self.master.after(0, self.update_emotion_display, emotion, confidence, probabilities)

                # 保存历史记录
                self.save_emotion_record(emotion, probabilities)

                # 检查警报
                self.check_emotion_alert(emotion, confidence)

            # 转换并显示图像
            img = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            img = Image.fromarray(img)
            img = img.resize((640, 480))
            img_tk = ImageTk.PhotoImage(image=img)

            self.master.after(0, self.update_video_display, img_tk)

    def recognize_emotion(self, image):
        """识别图像中的情绪"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.3, 5)

        if len(faces) > 0:
            (x, y, w, h) = max(faces, key=lambda f: f[2] * f[3])

            face_roi = image[y:y + h, x:x + w]
            face_img = cv2.resize(face_roi, (48, 48))

            img_np = face_img.astype(np.float32) / 255.0
            img_input = img_np.transpose((2, 0, 1))
            img_input = np.expand_dims(img_input, axis=0)
            img_input = Tensor(img_input, dtype=ms.float32)

            output = net(img_input)
            probabilities = nn.Softmax()(output).asnumpy()[0]

            max_idx = np.argmax(probabilities)
            emotion = categories[max_idx]
            confidence = probabilities[max_idx]

            return {
                'emotion': emotion,
                'confidence': confidence,
                'probabilities': probabilities,
                'face_location': (x, y, w, h)
            }

        return None

    def draw_chinese_text(self, img, emotion, confidence, position):
        """在图像上绘制中文文字"""
        try:
            # 将OpenCV图像转换为PIL图像
            img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))

            # 创建绘图对象
            from PIL import ImageDraw, ImageFont
            draw = ImageDraw.Draw(img_pil)

            # 尝试加载中文字体
            font_size = 24
            try:
                # 尝试使用本地字体文件
                if os.path.exists("SimHei.ttf"):
                    font = ImageFont.truetype("SimHei.ttf", font_size)
                else:
                    # 尝试使用系统字体
                    font_paths = [
                        "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
                        "C:/Windows/Fonts/simsun.ttc",  # 宋体
                        "C:/Windows/Fonts/simhei.ttf",  # 黑体
                    ]
                    font = None
                    for font_path in font_paths:
                        if os.path.exists(font_path):
                            font = ImageFont.truetype(font_path, font_size)
                            break

                    if font is None:
                        # 使用默认字体
                        font = ImageFont.load_default()
            except:
                # 如果加载字体失败，使用默认字体
                font = ImageFont.load_default()

            # 准备文字内容
            text = f"{emotion}: {confidence:.2f}"

            # 获取文字尺寸
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            # 调整位置，确保文字在图像范围内
            x, y = position
            if y < text_height + 10:
                y = text_height + 10
            if x + text_width > img.shape[1]:
                x = img.shape[1] - text_width - 10

            # 绘制背景矩形
            bg_color = (0, 0, 0, 128)  # 半透明黑色背景
            draw.rectangle([x - 5, y - text_height - 5, x + text_width + 5, y + 5], fill=bg_color)

            # 绘制文字
            text_color = (0, 255, 0)  # 绿色文字
            draw.text((x, y - text_height), text, font=font, fill=text_color)

            # 将PIL图像转换回OpenCV格式
            img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
            return img_cv

        except Exception as e:
            print(f"绘制中文文字失败: {e}")
            # 如果绘制失败，使用英文标签作为备选
            emotion_english = {'生气': 'Angry', '开心': 'Happy', '悲伤': 'Sad'}.get(emotion, emotion)
            emotion_text = f"{emotion_english}: {confidence:.2f}"
            cv2.putText(img, emotion_text, position,
                        cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
            return img

    def update_emotion_display(self, emotion, confidence, probabilities):
        """更新情绪显示"""
        self.current_emotion = emotion
        self.probabilities = probabilities

        self.emotion_var.set(emotion)
        self.confidence_var.set(f"{confidence:.2f}")

        # 更新概率显示
        for i, category in enumerate(categories):
            self.prob_vars[category].set(f"{probabilities[i]:.2f}")

        # 获取并显示建议
        suggestion = self.get_emotion_suggestion(emotion)
        self.suggestion_var.set(suggestion)

        # 根据情绪设置颜色
        color = emotion_colors.get(emotion, '#000000')
        self.emotion_label.configure(foreground=color)

    def update_video_display(self, img_tk):
        """更新视频显示"""
        self.video_label.configure(image=img_tk, text="")
        self.video_label.image = img_tk

    def get_emotion_suggestion(self, emotion):
        """获取情绪建议"""
        if emotion in emotion_suggestions:
            return random.choice(emotion_suggestions[emotion])
        return "保持积极的心态！"

    def save_emotion_record(self, emotion, probabilities):
        """保存情绪记录到历史"""
        timestamp = datetime.datetime.now()
        self.emotion_history.append(probabilities.copy())
        self.emotion_timestamps.append(timestamp)

        # 限制历史记录数量
        if len(self.emotion_history) > 1000:
            self.emotion_history.pop(0)
            self.emotion_timestamps.pop(0)

    def check_emotion_alert(self, emotion, confidence):
        """检查情绪警报"""
        if not self.settings["alert_enabled"]:
            # 如果警报未启用，显示正常状态
            if hasattr(self, 'alert_var'):
                self.alert_var.set("警报已禁用")
                self.alert_label.configure(foreground="gray")
            return

        thresholds = self.settings["alert_thresholds"]
        if emotion in thresholds and confidence >= thresholds[emotion]:
            suggestion = self.get_emotion_suggestion(emotion)

            # 在警报区域显示警报信息
            alert_message = f"⚠️ {emotion}情绪警报！\n建议：{suggestion}"
            if hasattr(self, 'alert_var'):
                self.alert_var.set(alert_message)
                self.alert_label.configure(foreground="red")

            # 在状态栏也显示简短警报信息
            status_message = f"⚠️ 情绪警报：检测到强烈的{emotion}情绪！"
            self.update_status(status_message)

            # 在控制台输出警报信息
            print(f"情绪警报 - {emotion}: 检测到强烈的{emotion}情绪！建议：{suggestion}")
        else:
            # 如果没有触发警报，显示正常状态
            if hasattr(self, 'alert_var'):
                self.alert_var.set("无警报")
                self.alert_label.configure(foreground="green")

    def toggle_detection(self):
        """切换检测状态"""
        if self.is_running:
            self.stop_detection()
        else:
            self.start_video_loop()

    def stop_detection(self):
        """停止检测"""
        self.is_running = False
        if self.cap:
            self.cap.release()
            self.cap = None
        self.update_status("检测已停止")

    def save_screenshot(self):
        """保存当前帧截图"""
        if not self.cap or not self.is_running:
            messagebox.showinfo("提示", "请先启动情绪检测")
            return

        ret, frame = self.cap.read()
        if ret:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{self.current_emotion}.png"
            filepath = str(self.screenshots_dir / filename)

            cv2.imwrite(filepath, frame)
            self.update_status(f"已保存截图: {filename}")
            messagebox.showinfo("成功", f"截图已保存: {filename}")

    def update_status(self, message):
        """更新状态栏消息"""
        if hasattr(self, 'status'):
            self.status.config(text=message)

    def save_settings(self):
        """保存设置"""
        self.settings = {
            "alert_enabled": self.alert_enabled_var.get(),
            "alert_thresholds": {
                "生气": self.anger_threshold_var.get(),
                "悲伤": self.sad_threshold_var.get()
            },
            "camera_index": self.camera_index_var.get(),
            "auto_save_screenshots": False,
            "screenshot_interval": 60
        }
        messagebox.showinfo("成功", "设置已保存")

    def test_camera(self):
        """测试摄像头"""
        camera_index = self.camera_index_var.get()
        test_cap = cv2.VideoCapture(camera_index)

        if test_cap.isOpened():
            messagebox.showinfo("测试结果", f"摄像头 {camera_index} 工作正常")
            test_cap.release()
        else:
            messagebox.showerror("测试结果", f"摄像头 {camera_index} 无法打开")

    def load_history_data(self):
        """加载历史数据"""
        self.history_listbox.delete(0, tk.END)

        # 显示当前会话的历史记录
        for i, (timestamp, probabilities) in enumerate(zip(self.emotion_timestamps, self.emotion_history)):
            max_idx = np.argmax(probabilities)
            emotion = categories[max_idx]
            confidence = probabilities[max_idx]

            display_text = f"{timestamp.strftime('%H:%M:%S')} - {emotion} ({confidence:.2f})"
            self.history_listbox.insert(tk.END, display_text)

    def clear_history(self):
        """清除历史记录"""
        if messagebox.askyesno("确认", "确定要清除当前会话的历史记录吗？"):
            self.emotion_history.clear()
            self.emotion_timestamps.clear()
            self.history_listbox.delete(0, tk.END)
            messagebox.showinfo("成功", "历史记录已清除")

    def open_folder_and_recognize(self):
        """打开文件夹并识别照片"""
        folder_path = filedialog.askdirectory(title="选择包含人脸照片的文件夹")

        if not folder_path:
            return

        # 支持的图片格式
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = []

        for ext in image_extensions:
            image_files.extend(Path(folder_path).glob(f"*{ext}"))
            image_files.extend(Path(folder_path).glob(f"*{ext.upper()}"))

        if not image_files:
            messagebox.showinfo("提示", "所选文件夹中没有找到图片文件")
            return

        # 创建结果窗口
        result_window = tk.Toplevel(self.master)
        result_window.title("批量识别结果")
        result_window.geometry("600x400")

        # 创建结果列表
        result_frame = ttk.Frame(result_window)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        chinese_font = self.get_chinese_font()
        result_listbox = tk.Listbox(result_frame, font=(chinese_font, 10))
        result_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=result_listbox.yview)
        result_listbox.configure(yscrollcommand=result_scrollbar.set)

        result_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 处理图片
        for image_file in image_files:
            try:
                image = cv2.imread(str(image_file))
                if image is not None:
                    result = self.recognize_emotion(image)
                    if result:
                        emotion = result['emotion']
                        confidence = result['confidence']
                        result_text = f"{image_file.name} - {emotion} ({confidence:.2f})"
                    else:
                        result_text = f"{image_file.name} - 未检测到人脸"
                else:
                    result_text = f"{image_file.name} - 无法读取图片"
            except Exception as e:
                result_text = f"{image_file.name} - 处理错误: {str(e)}"

            result_listbox.insert(tk.END, result_text)
            result_window.update()  # 实时更新显示

        messagebox.showinfo("完成", f"批量识别完成，共处理 {len(image_files)} 张图片")

    def safe_exit(self):
        """安全退出应用程序"""
        self.is_running = False

        if self.cap:
            self.cap.release()
            self.cap = None

        self.master.destroy()


# 主函数
def main():
    # 设置系统编码
    import sys
    import locale

    try:
        # 设置控制台编码为UTF-8
        if sys.platform.startswith('win'):
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
    except:
        pass

    # 确保中文显示正常
    plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]

    # 创建主窗口
    root = tk.Tk()

    # 设置窗口编码
    try:
        root.tk.call('encoding', 'system', 'utf-8')
    except:
        pass

    # 加载模型
    global net
    net = CNN()

    # 尝试加载预训练模型
    try:
        param_dict = load_checkpoint("emotion_model.ckpt")
        load_param_into_net(net, param_dict)
        print("已加载预训练模型")
    except Exception as e:
        print(f"加载预训练模型失败: {e}")
        print("使用未训练的模型")

    # 创建应用
    app = EmotionUI(root)

    # 运行主循环
    root.mainloop()


if __name__ == "__main__":
    main()
