# MySQL 安装和配置指南

## Windows 系统

### 方法一：使用 MySQL Installer（推荐）

1. **下载 MySQL Installer**
   - 访问 [MySQL官网](https://dev.mysql.com/downloads/installer/)
   - 下载 `mysql-installer-web-community-8.0.xx.x.msi`

2. **安装 MySQL**
   - 运行下载的安装程序
   - 选择 "Developer Default" 或 "Server only"
   - 按照向导完成安装

3. **配置 MySQL**
   - 设置 root 用户密码（请记住这个密码）
   - 选择端口（默认 3306）
   - 完成配置

4. **启动 MySQL 服务**
   - 按 `Win + R`，输入 `services.msc`
   - 找到 MySQL80 服务，确保状态为"正在运行"

### 方法二：使用 XAMPP

1. **下载 XAMPP**
   - 访问 [XAMPP官网](https://www.apachefriends.org/)
   - 下载并安装 XAMPP

2. **启动 MySQL**
   - 打开 XAMPP Control Panel
   - 点击 MySQL 的 "Start" 按钮

## Linux 系统 (Ubuntu/Debian)

```bash
# 更新包列表
sudo apt update

# 安装 MySQL Server
sudo apt install mysql-server

# 安全配置
sudo mysql_secure_installation

# 启动 MySQL 服务
sudo systemctl start mysql

# 设置开机自启
sudo systemctl enable mysql

# 登录 MySQL
sudo mysql -u root -p
```

## macOS 系统

### 使用 Homebrew

```bash
# 安装 Homebrew（如果未安装）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装 MySQL
brew install mysql

# 启动 MySQL 服务
brew services start mysql

# 安全配置
mysql_secure_installation
```

## 创建数据库用户（可选）

如果您不想使用 root 用户，可以创建专用用户：

```sql
-- 登录 MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE emotion_recognition CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'emotion_user'@'localhost' IDENTIFIED BY 'your_password';

-- 授权
GRANT ALL PRIVILEGES ON emotion_recognition.* TO 'emotion_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

## 测试连接

使用以下命令测试 MySQL 是否正常工作：

```bash
mysql -u root -p -e "SELECT VERSION();"
```

## 常见问题解决

### 1. "Access denied for user 'root'@'localhost'"

**解决方案：**
```bash
# 停止 MySQL 服务
sudo systemctl stop mysql

# 安全模式启动
sudo mysqld_safe --skip-grant-tables &

# 登录并重置密码
mysql -u root
USE mysql;
UPDATE user SET authentication_string=PASSWORD('new_password') WHERE User='root';
FLUSH PRIVILEGES;
EXIT;

# 重启 MySQL
sudo systemctl restart mysql
```

### 2. "Can't connect to MySQL server"

**检查项目：**
- MySQL 服务是否启动
- 端口 3306 是否被占用
- 防火墙设置

### 3. Windows 服务启动失败

**解决方案：**
- 以管理员身份运行命令提示符
- 执行：`net start mysql80`
- 或在服务管理器中手动启动

## 配置文件示例

创建 `.env` 文件（在项目根目录）：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=emotion_recognition

# 应用程序配置
SECRET_KEY=your-secret-key-here
```

## 验证安装

运行以下 Python 代码验证连接：

```python
import pymysql

try:
    connection = pymysql.connect(
        host='localhost',
        port=3306,
        user='root',
        password='your_password',
        charset='utf8mb4'
    )
    print("✓ MySQL 连接成功")
    connection.close()
except Exception as e:
    print(f"✗ MySQL 连接失败: {e}")
```

## 下一步

1. 确保 MySQL 服务正在运行
2. 运行 `python init_database.py` 初始化数据库
3. 启动情绪识别系统

如果仍有问题，请检查：
- MySQL 服务状态
- 用户名和密码
- 网络连接
- 防火墙设置
