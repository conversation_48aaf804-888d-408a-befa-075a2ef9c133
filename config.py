"""
配置文件
包含数据库连接配置和应用程序设置
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库配置
DATABASE_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'emotion_recognition'),
    'charset': 'utf8mb4'
}

# 应用程序配置
APP_CONFIG = {
    'secret_key': os.getenv('SECRET_KEY', 'your-secret-key-here'),
    'session_timeout': 3600,  # 会话超时时间（秒）
    'max_login_attempts': 5,  # 最大登录尝试次数
    'password_min_length': 6,  # 密码最小长度
}

# 情绪识别配置
EMOTION_CONFIG = {
    'categories': ['生气', '开心', '悲伤'],
    'colors': {
        '生气': '#FF5252',  # 红色
        '开心': '#4CAF50',  # 绿色
        '悲伤': '#2196F3',  # 蓝色
    },
    'icons': {
        '生气': '😠',
        '开心': '😄',
        '悲伤': '😢'
    },
    'suggestions': {
        '生气': [
            "深呼吸，慢慢数到十",
            "听一些舒缓的音乐",
            "出去散步，呼吸新鲜空气",
            "做一些放松的运动，如瑜伽",
            "与朋友聊天，分享你的感受"
        ],
        '开心': [
            "保持这种积极的心态！",
            "分享你的快乐给身边的人",
            "做一些你喜欢的事情",
            "记录下这美好的时刻",
            "继续保持乐观的态度"
        ],
        '悲伤': [
            "允许自己感受这种情绪",
            "找一个安静的地方休息一下",
            "与信任的朋友或家人交谈",
            "做一些让你感到舒适的事情",
            "记住这种感觉是暂时的"
        ]
    }
}

# 文件路径配置
PATHS = {
    'data_dir': 'emotion_data',
    'screenshots_dir': 'emotion_data/screenshots',
    'model_path': 'emotion_model.ckpt',
    'font_path': 'SimHei.ttf'
}
