"""
情绪识别系统启动脚本
检查环境和依赖，然后启动主程序
"""
import os
import sys
import subprocess

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        return False
    print(f"✓ Python版本: {sys.version}")
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'tkinter',
        'cv2',
        'mindspore', 
        'numpy',
        'PIL',
        'matplotlib',
        'pymysql',
        'bcrypt',
        'dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'PIL':
                from PIL import Image
            elif package == 'dotenv':
                from dotenv import load_dotenv
            else:
                __import__(package)
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} (未安装)")
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def check_database_config():
    """检查数据库配置"""
    if not os.path.exists('.env'):
        print("✗ 未找到.env配置文件")
        print("请先运行以下命令之一:")
        print("  python test_mysql.py    # 测试MySQL连接")
        print("  python init_database.py # 初始化数据库")
        return False

    print("✓ 找到.env配置文件")

    # 测试数据库连接
    try:
        from dotenv import load_dotenv
        load_dotenv()
        from database import DatabaseManager

        db_manager = DatabaseManager()
        if db_manager.connect():
            print("✓ 数据库连接正常")
            db_manager.disconnect()
            return True
        else:
            print("✗ 数据库连接失败")
            print("请检查 .env 文件中的数据库配置")
            return False
    except Exception as e:
        print(f"✗ 数据库连接测试失败: {e}")
        return False

def check_model_file():
    """检查模型文件"""
    if not os.path.exists('emotion_model.ckpt'):
        print("⚠ 未找到emotion_model.ckpt模型文件")
        print("程序将使用未训练的模型运行")
        return True
    
    print("✓ 找到模型文件")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("情绪识别系统启动检查")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    print("\n检查依赖包...")
    if not check_dependencies():
        return False
    
    print("\n检查配置文件...")
    if not check_database_config():
        return False
    
    print("\n检查模型文件...")
    check_model_file()
    
    print("\n" + "=" * 50)
    print("所有检查完成，启动程序...")
    print("=" * 50)
    
    # 启动主程序
    try:
        from emotion_ui_optimized import main as run_main
        run_main()
    except Exception as e:
        print(f"启动程序时发生错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n程序已退出")
        sys.exit(0)
    except Exception as e:
        print(f"\n发生未知错误: {e}")
        input("按回车键退出...")
        sys.exit(1)
