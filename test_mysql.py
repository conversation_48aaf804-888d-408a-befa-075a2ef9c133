"""
MySQL 连接测试脚本
用于快速测试 MySQL 连接是否正常
"""
import sys

def test_mysql_connection():
    """测试 MySQL 连接"""
    print("=" * 50)
    print("MySQL 连接测试")
    print("=" * 50)
    
    # 检查 PyMySQL 是否安装
    try:
        import pymysql
        print("✓ PyMySQL 已安装")
    except ImportError:
        print("✗ PyMySQL 未安装")
        print("请运行: pip install PyMySQL")
        return False
    
    # 获取连接信息
    print("\n请输入 MySQL 连接信息:")
    host = input("主机地址 (默认: localhost): ").strip() or "localhost"
    port = input("端口 (默认: 3306): ").strip() or "3306"
    user = input("用户名 (默认: root): ").strip() or "root"
    password = input("密码: ").strip()
    
    try:
        port = int(port)
    except ValueError:
        print("✗ 端口必须是数字")
        return False
    
    # 测试连接
    print(f"\n正在连接到 {host}:{port}...")
    
    try:
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            charset='utf8mb4'
        )
        
        print("✓ 连接成功!")
        
        # 测试基本查询
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✓ MySQL 版本: {version[0]}")
        
        # 测试数据库创建权限
        try:
            with connection.cursor() as cursor:
                cursor.execute("CREATE DATABASE IF NOT EXISTS test_emotion_db")
                cursor.execute("DROP DATABASE test_emotion_db")
            print("✓ 具有数据库创建权限")
        except Exception as e:
            print(f"⚠ 数据库权限测试失败: {e}")
        
        connection.close()
        
        # 保存配置
        save_config = input("\n是否保存此配置到 .env 文件? (y/n): ").lower()
        if save_config == 'y':
            create_env_file(host, port, user, password)
        
        return True
        
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        print("\n可能的原因:")
        print("1. MySQL 服务未启动")
        print("2. 用户名或密码错误")
        print("3. 主机地址或端口错误")
        print("4. 防火墙阻止连接")
        return False

def create_env_file(host, port, user, password):
    """创建 .env 配置文件"""
    import os
    
    env_content = f"""# 数据库配置
DB_HOST={host}
DB_PORT={port}
DB_USER={user}
DB_PASSWORD={password}
DB_NAME=emotion_recognition

# 应用程序配置
SECRET_KEY=your-secret-key-{os.urandom(8).hex()}
"""
    
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✓ .env 文件已创建")
    except Exception as e:
        print(f"✗ 创建 .env 文件失败: {e}")

def main():
    """主函数"""
    try:
        if test_mysql_connection():
            print("\n" + "=" * 50)
            print("测试完成! 现在可以运行 init_database.py")
            print("=" * 50)
        else:
            print("\n" + "=" * 50)
            print("连接测试失败，请检查 MySQL 配置")
            print("参考 MYSQL_SETUP.md 获取安装帮助")
            print("=" * 50)
    except KeyboardInterrupt:
        print("\n\n测试已取消")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
