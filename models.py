"""
数据模型类
定义用户、情绪记录等数据模型
"""
import json
import uuid
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from database import DatabaseManager

class User:
    """用户模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def create_user(self, username: str, email: str, password_hash: str) -> Optional[int]:
        """创建新用户"""
        query = """
            INSERT INTO users (username, email, password_hash)
            VALUES (%s, %s, %s)
        """
        if self.db.execute_update(query, (username, email, password_hash)):
            # 获取新创建用户的ID
            result = self.db.execute_query("SELECT LAST_INSERT_ID() as id")
            if result:
                return result[0]['id']
        return None
    
    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """根据用户名获取用户信息"""
        query = "SELECT * FROM users WHERE username = %s"
        result = self.db.execute_query(query, (username,))
        return result[0] if result else None
    
    def get_user_by_email(self, email: str) -> Optional[Dict]:
        """根据邮箱获取用户信息"""
        query = "SELECT * FROM users WHERE email = %s"
        result = self.db.execute_query(query, (email,))
        return result[0] if result else None
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict]:
        """根据ID获取用户信息"""
        query = "SELECT * FROM users WHERE id = %s"
        result = self.db.execute_query(query, (user_id,))
        return result[0] if result else None
    
    def update_last_login(self, user_id: int) -> bool:
        """更新最后登录时间"""
        query = "UPDATE users SET last_login = %s WHERE id = %s"
        return self.db.execute_update(query, (datetime.now(), user_id))
    
    def update_login_attempts(self, user_id: int, attempts: int, locked_until: Optional[datetime] = None) -> bool:
        """更新登录尝试次数"""
        query = "UPDATE users SET login_attempts = %s, locked_until = %s WHERE id = %s"
        return self.db.execute_update(query, (attempts, locked_until, user_id))
    
    def is_user_locked(self, user_id: int) -> bool:
        """检查用户是否被锁定"""
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        if user['locked_until'] and user['locked_until'] > datetime.now():
            return True
        return False

class EmotionRecord:
    """情绪记录模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def save_emotion_record(self, user_id: int, emotion: str, confidence: float, 
                          probabilities: List[float], session_id: str, 
                          screenshot_path: Optional[str] = None) -> bool:
        """保存情绪记录"""
        query = """
            INSERT INTO emotion_records 
            (user_id, emotion, confidence, probabilities, session_id, screenshot_path)
            VALUES (%s, %s, %s, %s, %s, %s)
        """
        probabilities_json = json.dumps(probabilities)
        return self.db.execute_update(query, (
            user_id, emotion, confidence, probabilities_json, session_id, screenshot_path
        ))
    
    def get_user_emotion_history(self, user_id: int, limit: int = 100) -> List[Dict]:
        """获取用户情绪历史记录"""
        query = """
            SELECT * FROM emotion_records 
            WHERE user_id = %s 
            ORDER BY timestamp DESC 
            LIMIT %s
        """
        result = self.db.execute_query(query, (user_id, limit))
        
        # 解析JSON格式的概率数据
        if result:
            for record in result:
                if record['probabilities']:
                    record['probabilities'] = json.loads(record['probabilities'])
        
        return result or []
    
    def get_emotion_statistics_by_date(self, user_id: int, start_date: date, end_date: date) -> List[Dict]:
        """获取指定日期范围的情绪统计"""
        query = """
            SELECT 
                DATE(timestamp) as date,
                emotion,
                COUNT(*) as count,
                AVG(confidence) as avg_confidence
            FROM emotion_records 
            WHERE user_id = %s AND DATE(timestamp) BETWEEN %s AND %s
            GROUP BY DATE(timestamp), emotion
            ORDER BY date DESC, emotion
        """
        return self.db.execute_query(query, (user_id, start_date, end_date)) or []
    
    def get_recent_emotions(self, user_id: int, hours: int = 24) -> List[Dict]:
        """获取最近几小时的情绪记录"""
        query = """
            SELECT * FROM emotion_records 
            WHERE user_id = %s AND timestamp >= DATE_SUB(NOW(), INTERVAL %s HOUR)
            ORDER BY timestamp DESC
        """
        result = self.db.execute_query(query, (user_id, hours))
        
        if result:
            for record in result:
                if record['probabilities']:
                    record['probabilities'] = json.loads(record['probabilities'])
        
        return result or []

class UserSession:
    """用户会话模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def create_session(self, user_id: int) -> str:
        """创建新会话"""
        session_id = str(uuid.uuid4())
        query = """
            INSERT INTO user_sessions (user_id, session_id)
            VALUES (%s, %s)
        """
        if self.db.execute_update(query, (user_id, session_id)):
            return session_id
        return None
    
    def end_session(self, session_id: str, total_detections: int = 0) -> bool:
        """结束会话"""
        # 计算会话持续时间
        query_duration = """
            SELECT TIMESTAMPDIFF(SECOND, start_time, NOW()) as duration
            FROM user_sessions 
            WHERE session_id = %s
        """
        result = self.db.execute_query(query_duration, (session_id,))
        duration = result[0]['duration'] if result else 0
        
        # 更新会话结束时间和统计信息
        query = """
            UPDATE user_sessions 
            SET end_time = NOW(), duration = %s, total_detections = %s
            WHERE session_id = %s
        """
        return self.db.execute_update(query, (duration, total_detections, session_id))
    
    def get_user_sessions(self, user_id: int, limit: int = 10) -> List[Dict]:
        """获取用户会话历史"""
        query = """
            SELECT * FROM user_sessions 
            WHERE user_id = %s 
            ORDER BY start_time DESC 
            LIMIT %s
        """
        return self.db.execute_query(query, (user_id, limit)) or []
