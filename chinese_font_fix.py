import tkinter as tk
import tkinter.font as tkFont

def get_chinese_font():
    """获取可用的中文字体"""
    # 尝试不同的中文字体（使用正确的字体名称）
    fonts_to_try = [
        '微软雅黑',           # Microsoft YaHei
        '宋体',              # SimSun  
        '黑体',              # SimHei
        '楷体',              # KaiTi
        '仿宋',              # FangSong
        'Microsoft YaHei UI',
        'Segoe UI'
    ]
    
    # 检查系统字体
    available_fonts = tkFont.families()
    
    for font in fonts_to_try:
        if font in available_fonts:
            print(f"使用字体: {font}")
            return font
    
    # 如果都没有，返回默认字体
    print("使用默认字体: TkDefaultFont")
    return "TkDefaultFont"

def test_chinese_display():
    """测试中文显示"""
    root = tk.Tk()
    root.title("中文字体测试")
    root.geometry("400x300")
    
    # 获取中文字体
    chinese_font = get_chinese_font()
    
    # 测试文本
    test_texts = [
        "生气",
        "开心", 
        "悲伤",
        "情绪识别系统",
        "当前情绪状态",
        "情绪建议",
        "情绪概率",
        "情绪警报"
    ]
    
    # 创建标签测试中文显示
    for i, text in enumerate(test_texts):
        label = tk.Label(root, text=text, font=(chinese_font, 14))
        label.pack(pady=5)
    
    # 添加退出按钮
    tk.Button(root, text="退出", command=root.quit, 
             font=(chinese_font, 12)).pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    test_chinese_display()
