import os
from PIL import Image
import numpy as np
import mindspore as ms
from mindspore import dataset as ds
from mindspore.common.initializer import Normal
import mindspore.nn as nn
from mindspore.train import Model
from mindspore.train.callback import LossMonitor, TimeMonitor
import mindspore.ops as ops
from mindspore import dtype as mstype
from mindspore.train.serialization import save_checkpoint
from sklearn.metrics import classification_report, confusion_matrix

# 自定义 Top1Accuracy 度量指标
class Top1Accuracy(nn.Metric):
    def __init__(self):
        super(Top1Accuracy, self).__init__()
        self.clear()

    def clear(self):
        self._correct_num = 0
        self._total_num = 0

    def update(self, *inputs):
        y_pred, y = inputs
        indices = ops.ArgMaxWithValue(axis=-1)(y_pred)[0]
        correct = ops.Equal()(indices, y)
        self._correct_num += ops.ReduceSum()(ops.Cast()(correct, mstype.int32))
        self._total_num += y.shape[0]

    def eval(self):
        if self._total_num == 0:
            raise RuntimeError("Metric can't be evaluated because 'update' has not been called.")
        return self._correct_num / self._total_num

# 数据路径
train_data_dir = r'D:\HUAWIE\excise\archive\train'
test_data_dir = r'D:\HUAWIE\excise\archive\test'
categories = ['angry', 'happy', 'sad']  # 只使用这三种情绪

# 加载数据
def load_data(data_dir, categories):
    data = []
    labels = []
    for idx, category in enumerate(categories):
        category_path = os.path.join(data_dir, category)
        for filename in os.listdir(category_path):
            if filename.endswith('.png'):
                img_path = os.path.join(category_path, filename)
                img = Image.open(img_path).convert('RGB')
                img = img.resize((48, 48))  # 可以根据需要调整尺寸
                img = np.array(img) / 255.0  # 归一化
                data.append(img)
                labels.append(idx)
    return np.array(data), np.array(labels)

train_data, train_labels = load_data(train_data_dir, categories)
test_data, test_labels = load_data(test_data_dir, categories)

# 构建数据集
def create_dataset(data, labels, batch_size=32, is_train=True):
    dataset = ds.NumpySlicesDataset((data, labels), column_names=["image", "label"], shuffle=is_train)
    transforms_list = [
        ds.vision.Resize((48, 48)),
        ds.vision.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5]),
        ds.vision.HWC2CHW()
    ]
    if is_train:
        transforms_list.insert(0, ds.vision.RandomHorizontalFlip(prob=0.5))
        transforms_list.insert(1, ds.vision.RandomRotation(degrees=10))
    dataset = dataset.map(operations=transforms_list, input_columns="image")
    dataset = dataset.batch(batch_size)
    return dataset

train_dataset = create_dataset(train_data, train_labels, batch_size=32, is_train=True)
test_dataset = create_dataset(test_data, test_labels, batch_size=32, is_train=False)

# 定义模型
class CNN(nn.Cell):
    def __init__(self):
        super(CNN, self).__init__()
        self.conv1 = nn.Conv2d(3, 32, 3, pad_mode='same', weight_init=Normal(0.02))
        self.conv2 = nn.Conv2d(32, 64, 3, pad_mode='same', weight_init=Normal(0.02))
        self.conv3 = nn.Conv2d(64, 128, 3, pad_mode='same', weight_init=Normal(0.02))
        self.fc1 = nn.Dense(128 * 6 * 6, 512)
        self.fc2 = nn.Dense(512, len(categories))
        self.relu = nn.ReLU()
        self.max_pool2d = nn.MaxPool2d(kernel_size=2, stride=2)
        self.flatten = nn.Flatten()

    def construct(self, x):
        x = self.conv1(x)
        x = self.relu(x)
        x = self.max_pool2d(x)
        x = self.conv2(x)
        x = self.relu(x)
        x = self.max_pool2d(x)
        x = self.conv3(x)
        x = self.relu(x)
        x = self.max_pool2d(x)
        x = self.flatten(x)
        x = self.fc1(x)
        x = self.relu(x)
        x = self.fc2(x)
        return x

net = CNN()

# 设置训练参数
learning_rate = 0.001
epochs = 40
batch_size = 32

# 定义损失函数和优化器
loss = nn.SoftmaxCrossEntropyWithLogits(sparse=True, reduction='mean')
opt = nn.Adam(net.trainable_params(), learning_rate=learning_rate)

# 创建模型，使用自定义的 Top1Accuracy 作为度量指标
model = Model(net, loss, opt, metrics={"Top_1_Accuracy": Top1Accuracy()})

# 开始训练
print("Starting training...")
model.train(epochs, train_dataset, callbacks=[LossMonitor(per_print_times=1), TimeMonitor()], dataset_sink_mode=False)

# 评估模型
# 测试模型的准确性
acc = model.eval(test_dataset)
print("Test Top-1 Accuracy:", acc)

# 获取预测结果
y_true = []
y_pred = []
for data, label in test_dataset.create_tuple_iterator():
    predictions = model.predict(data)
    y_true.extend(label.asnumpy())
    y_pred.extend(np.argmax(predictions.asnumpy(), axis=1))

# 打印分类报告和混淆矩阵
print("Classification Report:")
print(classification_report(y_true, y_pred, target_names=categories))
print("Confusion Matrix:")
print(confusion_matrix(y_true, y_pred))

# 保存模型
save_path = 'emotion_model.ckpt'
save_checkpoint(net, save_path)
print(f"模型已经保存到 {save_path}")