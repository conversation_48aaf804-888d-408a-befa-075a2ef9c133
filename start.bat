@echo off
chcp 65001 >nul
title 情绪识别系统

echo ================================================
echo 情绪识别系统
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查是否首次运行
if not exist .env (
    echo 检测到首次运行，正在初始化数据库...
    echo.
    python init_database.py
    if errorlevel 1 (
        echo 数据库初始化失败
        pause
        exit /b 1
    )
    echo.
)

REM 启动程序
echo 启动情绪识别系统...
python start.py

pause
