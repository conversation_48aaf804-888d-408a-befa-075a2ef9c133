# 情绪识别系统

基于深度学习的实时情绪识别系统，支持用户注册登录、数据存储和历史记录管理。

## 功能特性

- 🔐 **用户认证系统**: 支持用户注册、登录、密码修改
- 📊 **实时情绪检测**: 基于深度学习的情绪识别
- 💾 **数据存储**: MySQL数据库存储用户数据和情绪记录
- 📈 **历史记录**: 查看个人情绪历史和统计信息
- 🔔 **智能警报**: 情绪异常时的智能提醒
- 📸 **截图保存**: 自动保存检测时的截图
- 📁 **批量识别**: 支持文件夹批量图片识别
- 📤 **数据导出**: 支持个人数据导出

## 系统要求

- Python 3.7+
- MySQL 5.7+ 或 MariaDB 10.2+
- 摄像头设备

## 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd emotion_recognition
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 安装和配置MySQL

#### Windows:
1. 下载并安装 [MySQL Community Server](https://dev.mysql.com/downloads/mysql/)
2. 启动MySQL服务
3. 创建数据库用户（可选）

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

#### macOS:
```bash
brew install mysql
brew services start mysql
```

### 4. 初始化数据库
```bash
python init_database.py
```

按照提示输入数据库连接信息，脚本会自动创建数据库和表结构。

### 5. 运行程序
```bash
python emotion_ui_optimized.py
```

## 使用说明

### 首次使用

1. **注册账户**: 启动程序后，点击"注册"按钮创建新账户
2. **登录系统**: 使用注册的用户名和密码登录
3. **开始检测**: 点击"开始情绪检测"进行实时情绪识别

### 主要功能

#### 情绪检测
- 实时检测面部情绪（生气、开心、悲伤）
- 显示检测置信度和概率分布
- 提供情绪建议和警报

#### 历史记录
- 查看个人情绪历史记录
- 按时间排序显示检测结果
- 支持数据清除功能

#### 个人中心
- 查看账户信息和使用统计
- 修改登录密码
- 导出个人数据
- 清除历史记录

#### 系统设置
- 调整情绪警报阈值
- 配置摄像头参数
- 启用/禁用警报功能

## 数据库结构

### 用户表 (users)
- id: 用户ID
- username: 用户名
- email: 邮箱
- password_hash: 密码哈希
- created_at: 创建时间
- last_login: 最后登录时间
- is_active: 账户状态
- login_attempts: 登录尝试次数
- locked_until: 锁定截止时间

### 情绪记录表 (emotion_records)
- id: 记录ID
- user_id: 用户ID
- emotion: 检测到的情绪
- confidence: 置信度
- probabilities: 概率分布(JSON)
- timestamp: 检测时间
- session_id: 会话ID
- screenshot_path: 截图路径

### 用户会话表 (user_sessions)
- id: 会话ID
- user_id: 用户ID
- session_id: 会话标识
- start_time: 开始时间
- end_time: 结束时间
- duration: 持续时间
- total_detections: 总检测次数

## 配置文件

### .env 文件
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=emotion_recognition

# 应用程序配置
SECRET_KEY=your-secret-key
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库连接信息
   - 确认数据库用户权限

2. **摄像头无法打开**
   - 检查摄像头设备连接
   - 尝试不同的摄像头索引
   - 确认摄像头权限设置

3. **模型加载失败**
   - 确认 `emotion_model.ckpt` 文件存在
   - 检查MindSpore安装是否正确

4. **中文字体显示问题**
   - 确认系统安装了中文字体
   - 检查 `SimHei.ttf` 字体文件

### 日志查看

程序运行时会在控制台输出日志信息，包括：
- 数据库连接状态
- 模型加载状态
- 错误信息和警告

## 开发说明

### 项目结构
```
emotion_recognition/
├── emotion_ui_optimized.py    # 主程序文件
├── database.py                # 数据库操作类
├── auth.py                    # 用户认证模块
├── models.py                  # 数据模型
├── config.py                  # 配置文件
├── init_database.py           # 数据库初始化脚本
├── requirements.txt           # 依赖包列表
├── .env.example              # 环境变量示例
├── emotion_model.ckpt        # 训练好的模型文件
└── README.md                 # 说明文档
```

### 扩展开发

1. **添加新的情绪类别**: 修改 `config.py` 中的 `EMOTION_CONFIG`
2. **自定义数据库表**: 修改 `database.py` 中的表结构
3. **添加新功能**: 在 `emotion_ui_optimized.py` 中添加新的界面和功能

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- GitHub: your-github-username
