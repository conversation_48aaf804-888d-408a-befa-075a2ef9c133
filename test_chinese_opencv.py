import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os

def draw_chinese_text_on_image(img, text, position, font_size=24):
    """在图像上绘制中文文字"""
    try:
        # 将OpenCV图像转换为PIL图像
        img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        
        # 创建绘图对象
        draw = ImageDraw.Draw(img_pil)
        
        # 尝试加载中文字体
        try:
            # 尝试使用系统字体
            font_paths = [
                "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
                "C:/Windows/Fonts/simsun.ttc",  # 宋体
                "C:/Windows/Fonts/simhei.ttf",  # 黑体
            ]
            font = None
            for font_path in font_paths:
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, font_size)
                    print(f"使用字体: {font_path}")
                    break
            
            if font is None:
                # 使用默认字体
                font = ImageFont.load_default()
                print("使用默认字体")
        except Exception as e:
            print(f"加载字体失败: {e}")
            # 如果加载字体失败，使用默认字体
            font = ImageFont.load_default()
        
        # 获取文字尺寸
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 调整位置
        x, y = position
        if y < text_height + 10:
            y = text_height + 10
        if x + text_width > img.shape[1]:
            x = img.shape[1] - text_width - 10
        
        # 绘制背景矩形
        bg_color = (0, 0, 0, 180)  # 半透明黑色背景
        draw.rectangle([x-5, y-text_height-5, x+text_width+5, y+5], fill=bg_color)
        
        # 绘制文字
        text_color = (0, 255, 0)  # 绿色文字
        draw.text((x, y-text_height), text, font=font, fill=text_color)
        
        # 将PIL图像转换回OpenCV格式
        img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        return img_cv
        
    except Exception as e:
        print(f"绘制中文文字失败: {e}")
        # 如果绘制失败，使用英文标签作为备选
        cv2.putText(img, "Error: Chinese text", position,
                   cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 0, 255), 2)
        return img

def test_chinese_text():
    """测试中文文字绘制"""
    # 创建一个测试图像
    img = np.zeros((400, 600, 3), dtype=np.uint8)
    img.fill(50)  # 深灰色背景
    
    # 测试不同的中文文字
    test_texts = [
        ("生气: 0.85", (50, 80)),
        ("开心: 0.92", (50, 150)),
        ("悲伤: 0.76", (50, 220)),
        ("情绪识别系统", (50, 290)),
        ("当前检测结果", (50, 360))
    ]
    
    # 绘制所有测试文字
    for text, pos in test_texts:
        img = draw_chinese_text_on_image(img, text, pos)
    
    # 显示结果
    cv2.imshow('中文文字测试', img)
    print("按任意键关闭窗口...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()

if __name__ == "__main__":
    test_chinese_text()
