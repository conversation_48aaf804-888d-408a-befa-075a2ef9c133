# 快速启动指南

## 🚀 5分钟快速开始

### 第一步：安装依赖
```bash
pip install -r requirements.txt
```

### 第二步：安装和配置 MySQL

#### Windows 用户（推荐使用 XAMPP）
1. 下载并安装 [XAMPP](https://www.apachefriends.org/)
2. 启动 XAMPP Control Panel
3. 点击 MySQL 的 "Start" 按钮
4. 默认用户名：`root`，密码：空（直接回车）

#### 其他系统
参考 `MYSQL_SETUP.md` 文件

### 第三步：测试 MySQL 连接
```bash
python test_mysql.py
```
按提示输入数据库信息，测试成功后会自动创建 `.env` 配置文件。

### 第四步：初始化数据库
```bash
python init_database.py
```

### 第五步：启动程序
```bash
python start.py
```
或者在 Windows 上双击 `start.bat`

## 🔧 故障排除

### 问题1：数据库连接失败
**解决方案：**
1. 确认 MySQL 服务已启动
2. 运行 `python test_mysql.py` 测试连接
3. 检查用户名和密码是否正确

### 问题2：缺少依赖包
**解决方案：**
```bash
pip install pymysql bcrypt python-dotenv
```

### 问题3：模型文件缺失
**说明：** 程序会使用未训练的模型运行，功能正常但识别准确度较低。

### 问题4：中文显示异常
**解决方案：** 确保系统安装了中文字体，或将 `SimHei.ttf` 放在项目目录。

## 📋 默认配置

### XAMPP 用户
- 主机：localhost
- 端口：3306
- 用户名：root
- 密码：（空，直接回车）

### 标准 MySQL 安装
- 主机：localhost
- 端口：3306
- 用户名：root
- 密码：安装时设置的密码

## 🎯 使用流程

1. **注册账户**：首次使用需要注册
2. **登录系统**：使用注册的账户登录
3. **开始检测**：点击"开始情绪检测"
4. **查看历史**：在"查看情绪历史"中查看记录
5. **个人中心**：管理账户和查看统计

## 📞 获取帮助

如果遇到问题：
1. 查看控制台错误信息
2. 参考 `README.md` 详细文档
3. 检查 `MYSQL_SETUP.md` MySQL配置
4. 运行 `python test_mysql.py` 诊断连接问题

## 🔄 重新初始化

如果需要重新配置：
1. 删除 `.env` 文件
2. 运行 `python test_mysql.py`
3. 运行 `python init_database.py`
