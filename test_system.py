"""
系统功能测试脚本
测试数据库连接、用户认证等核心功能
"""
import sys
import os

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from database import DatabaseManager
        print("✓ database 模块导入成功")
    except Exception as e:
        print(f"✗ database 模块导入失败: {e}")
        return False
    
    try:
        from auth import AuthManager
        print("✓ auth 模块导入成功")
    except Exception as e:
        print(f"✗ auth 模块导入失败: {e}")
        return False
    
    try:
        from models import User, EmotionRecord, UserSession
        print("✓ models 模块导入成功")
    except Exception as e:
        print(f"✗ models 模块导入失败: {e}")
        return False
    
    try:
        from config import DATABASE_CONFIG, APP_CONFIG
        print("✓ config 模块导入成功")
    except Exception as e:
        print(f"✗ config 模块导入失败: {e}")
        return False
    
    return True

def test_database():
    """测试数据库功能"""
    print("\n测试数据库功能...")
    
    try:
        from database import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 测试连接
        if db_manager.connect():
            print("✓ 数据库连接成功")
            
            # 测试查询
            result = db_manager.execute_query("SELECT COUNT(*) as count FROM users")
            if result is not None:
                print(f"✓ 数据库查询成功，用户表中有 {result[0]['count']} 条记录")
            else:
                print("✗ 数据库查询失败")
                return False
            
            db_manager.disconnect()
            return True
        else:
            print("✗ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"✗ 数据库测试失败: {e}")
        return False

def test_auth():
    """测试认证功能"""
    print("\n测试认证功能...")
    
    try:
        from database import DatabaseManager
        from auth import AuthManager
        
        db_manager = DatabaseManager()
        auth_manager = AuthManager(db_manager)
        
        # 测试密码加密
        password = "test123"
        hashed = auth_manager.hash_password(password)
        if auth_manager.verify_password(password, hashed):
            print("✓ 密码加密/验证功能正常")
        else:
            print("✗ 密码加密/验证功能异常")
            return False
        
        # 测试输入验证
        valid, msg = auth_manager.validate_username("testuser")
        if valid:
            print("✓ 用户名验证功能正常")
        else:
            print(f"✗ 用户名验证功能异常: {msg}")
            return False
        
        valid, msg = auth_manager.validate_email("<EMAIL>")
        if valid:
            print("✓ 邮箱验证功能正常")
        else:
            print(f"✗ 邮箱验证功能异常: {msg}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 认证功能测试失败: {e}")
        return False

def test_models():
    """测试数据模型"""
    print("\n测试数据模型...")
    
    try:
        from database import DatabaseManager
        from models import User, EmotionRecord, UserSession
        
        db_manager = DatabaseManager()
        user_model = User(db_manager)
        
        # 测试用户查询（应该返回空结果，因为还没有用户）
        user = user_model.get_user_by_username("nonexistent")
        if user is None:
            print("✓ 用户模型查询功能正常")
        else:
            print("✗ 用户模型查询功能异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据模型测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("情绪识别系统功能测试")
    print("=" * 50)
    
    # 检查环境变量文件
    if not os.path.exists('.env'):
        print("✗ 未找到 .env 文件，请先运行 init_database.py")
        return False
    
    print("✓ 找到 .env 配置文件")
    
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✓ 环境变量加载成功")
    except Exception as e:
        print(f"✗ 环境变量加载失败: {e}")
        return False
    
    # 运行测试
    tests = [
        ("模块导入", test_imports),
        ("数据库功能", test_database),
        ("认证功能", test_auth),
        ("数据模型", test_models)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统功能正常")
        return True
    else:
        print("❌ 部分测试失败，请检查系统配置")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n测试已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        sys.exit(1)
    
    input("\n按回车键退出...")
