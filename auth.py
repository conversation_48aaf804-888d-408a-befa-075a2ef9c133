"""
用户认证模块
处理用户登录、注册、密码验证等功能
"""
import bcrypt
import re
from datetime import datetime, timedelta
from typing import Optional, Dict, Tu<PERSON>
from database import DatabaseManager
from models import User
from config import APP_CONFIG

class AuthManager:
    """认证管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.user_model = User(db_manager)
        self.current_user = None
    
    def hash_password(self, password: str) -> str:
        """加密密码"""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def validate_username(self, username: str) -> <PERSON><PERSON>[bool, str]:
        """验证用户名格式"""
        if not username:
            return False, "用户名不能为空"
        
        if len(username) < 3:
            return False, "用户名至少需要3个字符"
        
        if len(username) > 20:
            return False, "用户名不能超过20个字符"
        
        if not re.match(r'^[a-zA-Z0-9_\u4e00-\u9fa5]+$', username):
            return False, "用户名只能包含字母、数字、下划线和中文字符"
        
        return True, ""
    
    def validate_email(self, email: str) -> Tuple[bool, str]:
        """验证邮箱格式"""
        if not email:
            return False, "邮箱不能为空"
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return False, "邮箱格式不正确"
        
        return True, ""
    
    def validate_password(self, password: str) -> Tuple[bool, str]:
        """验证密码强度"""
        if not password:
            return False, "密码不能为空"
        
        min_length = APP_CONFIG['password_min_length']
        if len(password) < min_length:
            return False, f"密码至少需要{min_length}个字符"
        
        if len(password) > 50:
            return False, "密码不能超过50个字符"
        
        # 检查密码复杂度（至少包含字母和数字）
        has_letter = re.search(r'[a-zA-Z]', password)
        has_digit = re.search(r'\d', password)
        
        if not (has_letter and has_digit):
            return False, "密码必须包含至少一个字母和一个数字"
        
        return True, ""
    
    def register_user(self, username: str, email: str, password: str) -> Tuple[bool, str]:
        """注册新用户"""
        # 验证输入格式
        valid, msg = self.validate_username(username)
        if not valid:
            return False, msg
        
        valid, msg = self.validate_email(email)
        if not valid:
            return False, msg
        
        valid, msg = self.validate_password(password)
        if not valid:
            return False, msg
        
        # 检查用户名是否已存在
        if self.user_model.get_user_by_username(username):
            return False, "用户名已存在"
        
        # 检查邮箱是否已存在
        if self.user_model.get_user_by_email(email):
            return False, "邮箱已被注册"
        
        # 创建用户
        password_hash = self.hash_password(password)
        user_id = self.user_model.create_user(username, email, password_hash)
        
        if user_id:
            return True, "注册成功"
        else:
            return False, "注册失败，请稍后重试"
    
    def login_user(self, username: str, password: str) -> Tuple[bool, str, Optional[Dict]]:
        """用户登录"""
        if not username or not password:
            return False, "用户名和密码不能为空", None
        
        # 获取用户信息
        user = self.user_model.get_user_by_username(username)
        if not user:
            return False, "用户名或密码错误", None
        
        # 检查用户是否被锁定
        if self.user_model.is_user_locked(user['id']):
            return False, "账户已被锁定，请稍后再试", None
        
        # 检查账户是否激活
        if not user['is_active']:
            return False, "账户已被禁用", None
        
        # 验证密码
        if not self.verify_password(password, user['password_hash']):
            # 增加登录失败次数
            attempts = user['login_attempts'] + 1
            locked_until = None
            
            # 如果失败次数达到上限，锁定账户
            if attempts >= APP_CONFIG['max_login_attempts']:
                locked_until = datetime.now() + timedelta(minutes=30)  # 锁定30分钟
            
            self.user_model.update_login_attempts(user['id'], attempts, locked_until)
            
            if locked_until:
                return False, "登录失败次数过多，账户已被锁定30分钟", None
            else:
                return False, f"用户名或密码错误（剩余尝试次数：{APP_CONFIG['max_login_attempts'] - attempts}）", None
        
        # 登录成功，重置登录尝试次数并更新最后登录时间
        self.user_model.update_login_attempts(user['id'], 0, None)
        self.user_model.update_last_login(user['id'])
        
        # 设置当前用户
        self.current_user = user
        
        return True, "登录成功", user
    
    def logout_user(self):
        """用户登出"""
        self.current_user = None
    
    def get_current_user(self) -> Optional[Dict]:
        """获取当前登录用户"""
        return self.current_user
    
    def is_logged_in(self) -> bool:
        """检查是否已登录"""
        return self.current_user is not None
    
    def change_password(self, user_id: int, old_password: str, new_password: str) -> Tuple[bool, str]:
        """修改密码"""
        # 获取用户信息
        user = self.user_model.get_user_by_id(user_id)
        if not user:
            return False, "用户不存在"
        
        # 验证旧密码
        if not self.verify_password(old_password, user['password_hash']):
            return False, "原密码错误"
        
        # 验证新密码
        valid, msg = self.validate_password(new_password)
        if not valid:
            return False, msg
        
        # 更新密码
        new_password_hash = self.hash_password(new_password)
        query = "UPDATE users SET password_hash = %s WHERE id = %s"
        
        if self.db.execute_update(query, (new_password_hash, user_id)):
            return True, "密码修改成功"
        else:
            return False, "密码修改失败，请稍后重试"
